import { Veo3Template } from "@/types/veo-3";

export const veo3Templates: Veo3Template[] = [
  {
    id: "simple-room",
    name: {
      en: "IKEA Room Assembly",
      zh: "宜家房间组装"
    },
    description: {
      en: "A simple template for room assembly scenes with basic elements",
      zh: "用于房间组装场景的简单模板，包含基本元素"
    },
    category: "simple",
    schema: [
      {
        key: "description",
        type: "textarea",
        label: {
          en: "Scene Description",
          zh: "场景描述"
        },
        placeholder: {
          en: "Describe the overall scene...",
          zh: "描述整体场景..."
        },
        required: true
      },
      {
        key: "style",
        type: "text",
        label: {
          en: "Visual Style",
          zh: "视觉风格"
        },
        placeholder: {
          en: "e.g., cinematic, documentary, artistic, commercial",
          zh: "例如：电影级，纪录片，艺术，商业"
        }
      },
      {
        key: "camera",
        type: "text",
        label: {
          en: "Camera Setup",
          zh: "摄像机设置"
        },
        placeholder: {
          en: "e.g., fixed wide angle",
          zh: "例如：固定广角"
        }
      },
      {
        key: "lighting",
        type: "text",
        label: {
          en: "Lighting",
          zh: "灯光"
        },
        placeholder: {
          en: "e.g., natural warm with cool accents",
          zh: "例如：自然暖光配冷色调"
        }
      },
      {
        key: "room",
        type: "text",
        label: {
          en: "Room Type",
          zh: "房间类型"
        },
        placeholder: {
          en: "e.g., Scandinavian bedroom",
          zh: "例如：斯堪的纳维亚卧室"
        }
      },
      {
        key: "elements",
        type: "array",
        label: {
          en: "Room Elements",
          zh: "房间元素"
        },
        placeholder: {
          en: "Add room elements...",
          zh: "添加房间元素..."
        }
      },
      {
        key: "motion",
        type: "textarea",
        label: {
          en: "Motion Description",
          zh: "动作描述"
        },
        placeholder: {
          en: "Describe the movement and animation...",
          zh: "描述运动和动画..."
        }
      },
      {
        key: "ending",
        type: "text",
        label: {
          en: "Ending Scene",
          zh: "结尾场景"
        },
        placeholder: {
          en: "How the scene ends...",
          zh: "场景如何结束..."
        }
      },
      {
        key: "text",
        type: "text",
        label: {
          en: "Text Display",
          zh: "文字显示"
        },
        placeholder: {
          en: "",
          zh: ""
        }
      },
      {
        key: "keywords",
        type: "array",
        label: {
          en: "Keywords",
          zh: "关键词"
        },
        placeholder: {
          en: "Add keywords...",
          zh: "添加关键词..."
        }
      }
    ],
    defaultValues: {
      description: "Cinematic shot of a sunlit Scandinavian bedroom. A sealed IKEA box trembles, opens, and flat pack furniture assembles rapidly into a serene, styled room highlighted by a yellow IKEA throw on the bed. No text.",
      style: "cinematic",
      camera: "fixed wide angle",
      lighting: "natural warm with cool accents",
      room: "Scandinavian bedroom",
      elements: [
        "IKEA box (logo visible)",
        "bed with yellow throw",
        "bedside tables",
        "lamps",
        "wardrobe",
        "shelves",
        "mirror",
        "art",
        "rug",
        "curtains",
        "reading chair",
        "plants"
      ],
      motion: "box opens, furniture assembles precisely and rapidly",
      ending: "calm, modern space with yellow IKEA accent",
      text: "none",
      keywords: [
        "16:9",
        "IKEA",
        "Scandinavian",
        "fast assembly",
        "no text",
        "warm & cool tones"
      ]
    },
    sampleVideo: {
      coverImage: "https://r2.promptark.net/veo-3/IKEA-Room-Assembly.webp",
      videoUrl: "https://r2.promptark.net/veo-3/IKEA-Room-Assembly.mp4",
      title: {
        en: "IKEA Room Assembly Example",
        zh: "宜家房间组装示例"
      }
    }
  },
  {
    id: "advanced-commercial",
    name: {
      en: "Advanced Commercial",
      zh: "高级商业广告"
    },
    description: {
      en: "Advanced template for commercial videos with detailed cinematography",
      zh: "用于商业视频的高级模板，包含详细的摄影技术"
    },
    category: "advanced",
    schema: [
      {
        key: "shot",
        type: "object",
        label: {
          en: "Shot Configuration",
          zh: "镜头配置"
        },
        children: [
          {
            key: "composition",
            type: "textarea",
            label: {
              en: "Composition",
              zh: "构图"
            },
            placeholder: {
              en: "Describe the shot composition...",
              zh: "描述镜头构图..."
            }
          },
          {
            key: "lens",
            type: "text",
            label: {
              en: "Lens Type",
              zh: "镜头类型"
            },
            placeholder: {
              en: "e.g., anamorphic, 50mm",
              zh: "例如：变形镜头，50mm"
            }
          },
          {
            key: "frame_rate",
            type: "text",
            label: {
              en: "Frame Rate",
              zh: "帧率"
            },
            placeholder: {
              en: "e.g., 60fps",
              zh: "例如：60fps"
            }
          },
          {
            key: "camera_movement",
            type: "textarea",
            label: {
              en: "Camera Movement",
              zh: "摄像机运动"
            },
            placeholder: {
              en: "Describe camera movements...",
              zh: "描述摄像机运动..."
            }
          }
        ]
      },
      {
        key: "subject",
        type: "object",
        label: {
          en: "Subject Details",
          zh: "主体细节"
        },
        children: [
          {
            key: "description",
            type: "textarea",
            label: {
              en: "Subject Description",
              zh: "主体描述"
            },
            placeholder: {
              en: "Describe the main subject...",
              zh: "描述主要主体..."
            }
          },
          {
            key: "wardrobe",
            type: "text",
            label: {
              en: "Wardrobe",
              zh: "服装"
            },
            placeholder: {
              en: "Describe clothing and style...",
              zh: "描述服装和风格..."
            }
          },
          {
            key: "props",
            type: "text",
            label: {
              en: "Props",
              zh: "道具"
            },
            placeholder: {
              en: "List important props...",
              zh: "列出重要道具..."
            }
          }
        ]
      },
      {
        key: "scene",
        type: "object",
        label: {
          en: "Scene Settings",
          zh: "场景设置"
        },
        children: [
          {
            key: "location",
            type: "text",
            label: {
              en: "Location",
              zh: "地点"
            },
            placeholder: {
              en: "Describe the location...",
              zh: "描述地点..."
            }
          },
          {
            key: "time_of_day",
            type: "text",
            label: {
              en: "Time of Day",
              zh: "时间"
            },
            placeholder: {
              en: "Describe the time of day...",
              zh: "描述时间..."
            }
          },
          {
            key: "environment",
            type: "text",
            label: {
              en: "Environment",
              zh: "环境"
            },
            placeholder: {
              en: "Describe the environment...",
              zh: "描述环境..."
            }
          }
        ]
      },
      {
        key: "visual_details",
        type: "object",
        label: {
          en: "Visual Details",
          zh: "视觉细节"
        },
        children: [
          {
            key: "action",
            type: "textarea",
            label: {
              en: "Action Description",
              zh: "动作描述"
            },
            placeholder: {
              en: "Describe the main action...",
              zh: "描述主要动作..."
            }
          },
          {
            key: "special_effects",
            type: "text",
            label: {
              en: "Special Effects",
              zh: "特效"
            },
            placeholder: {
              en: "List special effects...",
              zh: "列出特效..."
            }
          },
          {
            key: "hair_clothing_motion",
            type: "text",
            label: {
              en: "Hair & Clothing Motion",
              zh: "头发和服装运动"
            },
            placeholder: {
              en: "Describe movement details...",
              zh: "描述运动细节..."
            }
          },
          {
            key: "lighting",
            type: "text",
            label: {
              en: "Lighting",
              zh: "灯光"
            },
            placeholder: {
              en: "Describe lighting setup...",
              zh: "描述灯光设置..."
            }
          },
          {
            key: "color_palette",
            type: "text",
            label: {
              en: "Color Palette",
              zh: "色彩调色板"
            },
            placeholder: {
              en: "Describe color scheme...",
              zh: "描述色彩方案..."
            }
          },
          {
            key: "tone",
            type: "text",
            label: {
              en: "Tone",
              zh: "基调"
            },
            placeholder: {
              en: "Describe overall tone...",
              zh: "描述整体基调..."
            }
          }
        ]
      },
      {
        key: "audio",
        type: "object",
        label: {
          en: "Audio Settings",
          zh: "音频设置"
        },
        children: [
          {
            key: "music",
            type: "textarea",
            label: {
              en: "Music",
              zh: "音乐"
            },
            placeholder: {
              en: "Describe music style and progression...",
              zh: "描述音乐风格和进展..."
            }
          },
          {
            key: "ambient",
            type: "text",
            label: {
              en: "Ambient Sound",
              zh: "环境音"
            },
            placeholder: {
              en: "Describe ambient sounds...",
              zh: "描述环境声音..."
            }
          },
          {
            key: "sound_effects",
            type: "text",
            label: {
              en: "Sound Effects",
              zh: "音效"
            },
            placeholder: {
              en: "List sound effects...",
              zh: "列出音效..."
            }
          },
          {
            key: "mix_level",
            type: "text",
            label: {
              en: "Mix Level",
              zh: "混音级别"
            },
            placeholder: {
              en: "Describe audio mixing...",
              zh: "描述音频混合..."
            }
          }
        ]
      },
      {
        key: "dialogue",
        type: "object",
        label: {
          en: "Dialogue",
          zh: "对话"
        },
        children: [
          {
            key: "character",
            type: "text",
            label: {
              en: "Character",
              zh: "角色"
            },
            placeholder: {
              en: "Character name or description...",
              zh: "角色名称或描述..."
            }
          },
          {
            key: "line",
            type: "textarea",
            label: {
              en: "Line",
              zh: "台词"
            },
            placeholder: {
              en: "Dialogue line...",
              zh: "对话台词..."
            }
          },
          {
            key: "subtitles",
            type: "select",
            label: {
              en: "Subtitles",
              zh: "字幕"
            },
            options: ["true", "false"]
          }
        ]
      }
    ],
    defaultValues: {
      shot: {
        composition: "starts in wide landscape with a centered runner, transitions to low-angle close tracking of feet, ends with high-angle logo reveal that morphs into product",
        lens: "anamorphic for wide kinetic sweep, 50mm for focused footwork tracking",
        frame_rate: "60fps",
        camera_movement: "ground-level tracking with runner, lateral sweep as body disintegrates, final smooth pullback from logo to sneaker"
      },
      subject: {
        description: "a runner sprints across shifting terrain, their movement generating energy trails that eventually form the Adidas logo before crystallizing into the product",
        wardrobe: "sleek modern athletic wear in black and white",
        props: "Adidas performance sneaker, streak-like energy trails representing momentum"
      },
      scene: {
        location: "abstract terrain morphing between urban pavement, track, and reflective void",
        time_of_day: "golden twilight with crisp, cinematic shadows",
        environment: "fluid and surreal, grounded but evolving with each stride"
      },
      visual_details: {
        action: "the runner's feet strike and release kinetic ripples that trail behind; their form begins to dissolve into motion lines that swirl upward into the Adidas three-stripe logo, which pulses and reassembles into a pristine sneaker in a zero-gravity moment",
        special_effects: "motion streak trails, slow-motion foot impacts, morphing energy particles, ripple crystallization of product",
        hair_clothing_motion: "athletic wear ripples in the slipstream; short hair flowing naturally with each stride",
        lighting: "backlit sunset with long directional shadows, warm edge lighting on moving form",
        color_palette: "deep blacks, soft whites, warm golds, electric silver accents",
        tone: "epic, modern, kinetically poetic"
      },
      audio: {
        music: "percussive electronic with heartbeat tempo, evolving into orchestral hit at product reveal",
        ambient: "distant wind, rhythmic breathing, subtle terrain textures",
        sound_effects: "impact thuds, trailing whooshes, logo crystallization chime",
        mix_level: "dynamic layering with a rising energy curve and crisp product punctuation"
      },
      dialogue: {
        character: "",
        line: "",
        subtitles: "false"
      }
    },
    sampleVideo: {
      coverImage: "https://r2.promptark.net/veo-3/Adidas-Commercial.webp",
      videoUrl: "https://r2.promptark.net/veo-3/Adidas-Commercial.mp4",
      title: {
        en: "Adidas Commercial Example",
        zh: "阿迪达斯商业广告示例"
      }
    }
  }
];
